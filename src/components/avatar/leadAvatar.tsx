import { LeadStatusBadge } from "@components/badge";
import type { LeadStatusType } from "@components/badge/leadStatusBadge";
import { Avatar } from "@components/common";

interface LeadAvatarProps {
  image: string;
  leadStatus: LeadStatusType;
}

export const LeadAvatar = ({ image, leadStatus }: LeadAvatarProps) => {
  return (
    <div className="flex flex-col items-center gap-1">
      <Avatar className="w-10" image={image} />
      <LeadStatusBadge type={leadStatus} />
    </div>
  );
};
